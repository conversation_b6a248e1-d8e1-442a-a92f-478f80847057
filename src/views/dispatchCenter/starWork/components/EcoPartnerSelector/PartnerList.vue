<template>
  <div class="partner-list-wrapper" :class="{ 'single-item': displayList.length === 1 }">
    <div v-for="(companyItem, index) in displayList" :key="index" class="box person-wrap">
      <div class="partner-selection">
        <a-radio-group :value="selectedValue">
          <a-radio :value="companyItem.contactPhone" @change="(e) => handleSelectionChange(e, companyItem)"
            :disabled="!isCompanyAvailable(companyItem)" />
        </a-radio-group>
        <span v-if="companyItem.ecopartnerName" class="company_left company-name font-weight-500"
          @click="handleCompanyDetail(companyItem)">
          <span class="company_underline">{{ companyItem.ecopartnerName }}</span>
          <span v-if="getCompanyStatusMessage(companyItem)" class="status-message">
            {{ getCompanyStatusMessage(companyItem) }}
          </span>
        </span>
      </div>

      <div class="company_right">
        <img width="20px" height="20px" src="@/assets/images/score.png" />
        <span>生态评分：<span class="score-value">{{ formatScore(companyItem) }}</span></span>
      </div>

      <a-select v-model:value="companyItem.contactName" @change="(value) => handleContactChange(value, companyItem)"
        :disabled="!isCompanyAvailable(companyItem)" placeholder="请选择联系人">
        <template v-for="(opt, index) in companyItem.contactList" :key="index">
          <a-select-option :value="getContactValue(opt)" :label="opt.contactName" :disabled="isContactDisabled(opt)">
            {{ opt.contactName }}
          </a-select-option>
        </template>
      </a-select>

      <span :style="{
        color: isCompanyAvailable(companyItem) ? '' : '#999', 'min-width': '120px', 'text-align': 'left'
      }">
        {{ companyItem.contactPhone }}
      </span>
    </div>

    <div v-if="displayList.length === 0" class="no-data">
      暂无数据
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'EcoPartnerList',
  props: {
    // 显示的公司列表
    companyList: {
      type: Array,
      default: () => []
    },
    // 当前选中的值
    selectedValue: {
      type: String,
      default: ''
    },
    // 被拒绝的公司ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 联系人选择模式 (userId 或 contactName)
    contactSelectMode: {
      type: String,
      default: 'userId'
    }
  },
  emits: ['selection-change', 'contact-change', 'company-detail'],
  setup(props, { emit }) {
    // 显示的列表
    const displayList = computed(() => props.companyList)

    // 检查公司是否可用
    const isCompanyAvailable = (company) => {
      return company.auth === 1 && company.sync === 1 && company.approve === 1
    }

    // 获取公司状态消息
    const getCompanyStatusMessage = (company) => {
      if (company.auth === 1 && company.sync === 1 && company.approve && company.approve !== 1) {
        return '生态厂商暂无该生态联系人！'
      } else if (company.auth === 0) {
        return '该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！'
      } else if (!(company.auth === 1 && company.sync === 1 && company.approve)) {
        return '生态厂商暂无该生态联系人！'
      }
      return ''
    }

    // 格式化评分
    const formatScore = (company) => {
      if (company.totalScore) {
        return company.totalScore
      } else if (company.introScore) {
        return company.introScore
      } else {
        return '-'
      }
    }

    // 获取联系人选择值
    const getContactValue = (contact) => {
      return props.contactSelectMode === 'userId' ? contact.userId : contact.contactName
    }

    // 检查联系人是否被禁用
    const isContactDisabled = (contact) => {
      const baseDisabled = contact.approve !== 1
      const rejectedDisabled = props.rejectCompanyIdList.some(
        (value) => value.userId === contact.userId
      )
      return baseDisabled || rejectedDisabled
    }

    // 处理选择变化
    const handleSelectionChange = (e, company) => {
      emit('selection-change', e, company)
    }

    // 处理联系人变化
    const handleContactChange = (value, company) => {
      emit('contact-change', value, company)
    }

    // 处理公司详情点击
    const handleCompanyDetail = (company) => {
      emit('company-detail', company)
    }

    return {
      displayList,
      isCompanyAvailable,
      getCompanyStatusMessage,
      formatScore,
      getContactValue,
      isContactDisabled,
      handleSelectionChange,
      handleContactChange,
      handleCompanyDetail
    }
  }
})
</script>

<style lang="scss" scoped>
.partner-list-wrapper {
  overflow-x: auto;

  &.single-item {
    overflow-x: visible;
  }
}

.box.person-wrap {
  width: max-content;
  min-width: 100%;
  padding: 0 38px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 26px;
  white-space: nowrap;
  justify-content: space-between;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.partner-list-wrapper.single-item .box.person-wrap {
  width: 100%;
  min-width: auto;
  white-space: normal;
}

.partner-selection {
  display: flex;
  width: 280px;
  align-items: center;
  gap: 8px;
}

.company_left {
  margin-right: 8px;
  font-weight: bold;

  .company_underline {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #40a9ff;
    }
  }

  .status-message {
    color: red;
    font-size: 12px;
    display: block;
    margin-top: 4px;
  }
}

.company_right {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  min-width: 156px;
  max-width: 176px;
  height: 28px;
  font-size: 15px;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
  border-radius: 20px;
  padding: 0 12px;

  .score-value {
    color: #FF9C39FF;
    font-weight: bold;
  }
}

.font-weight-500 {
  font-weight: 500;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}

:deep(.ant-select) {
  width: 140px;
}

:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border: 1px solid #d9d9d9 !important;
}
</style>
